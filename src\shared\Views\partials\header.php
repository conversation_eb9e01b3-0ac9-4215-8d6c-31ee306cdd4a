<?php
// Get domain data from Controller (passed via render method)
$domain = $domainData ?? [];
$currentDomain = $domain['currentDomain'] ?? 'unknown';
$homeUrl = $domain['homeUrl'] ?? '/';
$loginUrl = $domain['loginUrl'] ?? '/';
$depositUrl = $domain['depositUrl'] ?? '/';
$profileUrl = $domain['profileUrl'] ?? '/';
$historyUrl = $domain['historyUrl'] ?? '/';
$logoutUrl = $domain['logoutUrl'] ?? '/';
$domains = $domain['domains'] ?? [];

$payDomain = $domains['pay'] ?? '/';

?>

<!-- Main Header -->
<header class="bg-white shadow-sm border-b border-gray-100">
    <!-- Top Navigation -->
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Desktop Layout -->
        <div class="hidden md:flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="<?= $payDomain ?>" class="flex items-center" title="Trang chủ MtfGame">
                    <img src="<?= $this->asset('images/logo_mtf.png') ?>" alt="MtfGame" class="h-10 w-auto">
                </a>
            </div>

            <!-- Right Menu -->
            <div class="flex items-center space-x-4">
                    <!-- Language Switcher -->
                <div class="relative">
                    <button id="languageSwitcher" class="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-50 hover:bg-gray-100 border border-gray-200 transition-colors" title="<?= $currentLanguage === 'vi' ? 'Đổi ngôn ngữ' : 'Switch Language' ?>">
                        <span class="text-lg"><?= $availableLanguages[$currentLanguage ?? 'vi']['flag'] ?? '🌐' ?></span>
                        <span class="text-sm font-medium text-gray-700"><?= strtoupper($currentLanguage ?? 'VI') ?></span>
                        <i class="fas fa-chevron-down text-xs text-gray-400"></i>
                    </button>

                    <!-- Language Dropdown -->
                    <div id="languageDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible transform scale-95 transition-all duration-200 z-50">
                        <?php
                        $availableLanguages = $availableLanguages ?? [];
                        foreach ($availableLanguages as $langKey => $language):
                        ?>
                            <button class="language-option w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3 <?= $langKey === ($currentLanguage ?? 'vi') ? 'bg-blue-50 text-blue-600' : 'text-gray-700' ?> <?= $langKey === array_key_first($availableLanguages) ? 'rounded-t-lg' : '' ?> <?= $langKey === array_key_last($availableLanguages) ? 'rounded-b-lg' : '' ?>" data-language="<?= $langKey ?>">
                                <span class="text-lg"><?= $language['flag'] ?? '🌐' ?></span>
                                <span class="font-medium"><?= $language['name'] ?? ucfirst($langKey) ?></span>
                                <?php if ($langKey === ($currentLanguage ?? 'vi')): ?>
                                    <i class="fas fa-check ml-auto text-blue-600"></i>
                                <?php endif; ?>
                            </button>
                        <?php endforeach; ?>
                    </div>
                </div>

                    <?php if (!$this->isAuth()): ?>
                        <a href="<?= $loginUrl ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <?= $__('shared.header.login') ?>
                        </a>
                    <?php else: ?>
                        <a href="<?= $depositUrl ?>" class="bg-gradient-to-r from-emerald-500 to-blue-600 text-white px-6 py-2 rounded-lg hover:from-emerald-600 hover:to-blue-700 transition-all shadow-lg flex items-center space-x-2 font-semibold">
                            <i class="fas fa-gem"></i>
                            <span><?= $__('shared.header.deposit') ?></span>
                        </a>
                        <!-- User Menu -->
                        <div class="flex items-center space-x-4">
                            <!-- Balance Display -->
                            <div class="bg-gradient-to-r from-emerald-50 to-blue-50 border border-emerald-200 px-4 py-2 rounded-xl flex items-center space-x-3 shadow-sm">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-coins text-white text-xs"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-600"><?= $__('shared.header.balance') ?>:</span>
                                </div>
                                <span id="userBalance" class="text-lg font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                                    <?= $this->currency($this->user()['balance'] ?? 0, 'small') ?>
                                </span>
                                <button id="refreshBalanceBtn" class="group p-2 rounded-lg hover:bg-white/50 transition-all duration-200" title="<?= $__('shared.header.refresh_balance') ?>">
                                    <i class="fas fa-sync-alt text-sm text-gray-400 group-hover:text-emerald-500 transition-colors group-hover:rotate-180 duration-300"></i>
                                </button>
                            </div>

                            <!-- User Menu -->
                            <div class="relative group">
                                <button id="userMenuButton" class="flex items-center space-x-3 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 border border-gray-200 hover:border-blue-300 px-4 py-2 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md">
                                    <div class="relative">
                                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm">
                                            <i class="fas fa-user text-white text-sm"></i>
                                        </div>
                                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                    </div>
                                    <div class="flex flex-col items-start">
                                        <span class="text-sm font-semibold text-gray-700 group-hover:text-blue-600 transition-colors"><?= $this->escape($this->user()['username'] ?? 'User') ?></span>
                                        <span class="text-xs text-gray-500">Online</span>
                                    </div>
                                    <i class="fas fa-chevron-down text-xs text-gray-400 group-hover:text-blue-500 transition-all duration-300 transform group-hover:rotate-180"></i>
                                </button>

                                <!-- User Dropdown -->
                                <div id="userMenuDropdown" class="absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 opacity-0 invisible transform scale-95 translate-y-2 transition-all duration-300 z-50 backdrop-blur-sm">
                                    <div class="p-3">
                                        <!-- User Info Header -->
                                        <div class="flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl mb-3">
                                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="font-semibold text-gray-800"><?= $this->escape($this->user()['username'] ?? 'User') ?></div>
                                                <div class="text-sm text-gray-500"><?= $this->escape($this->user()['email'] ?? '') ?></div>
                                            </div>
                                        </div>

                                        <!-- Menu Items -->
                                        <div class="space-y-1">
                                            <a href="<?= $profileUrl ?>" class="flex items-center space-x-3 px-3 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-xl transition-all duration-200 group">
                                                <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 group-hover:bg-blue-200 transition-colors">
                                                    <i class="fas fa-user-cog text-blue-600 text-sm"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="font-medium"><?= $__('shared.header.profile') ?></div>
                                                    <div class="text-xs text-gray-500">Quản lý tài khoản</div>
                                                </div>
                                                <i class="fas fa-chevron-right text-xs text-gray-400 group-hover:text-blue-500 transition-colors"></i>
                                            </a>

                                            <a href="<?= $historyUrl ?>" class="flex items-center space-x-3 px-3 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 rounded-xl transition-all duration-200 group">
                                                <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-purple-100 group-hover:bg-purple-200 transition-colors">
                                                    <i class="fas fa-history text-purple-600 text-sm"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="font-medium"><?= $__('shared.header.history') ?></div>
                                                    <div class="text-xs text-gray-500">Lịch sử giao dịch</div>
                                                </div>
                                                <i class="fas fa-chevron-right text-xs text-gray-400 group-hover:text-purple-500 transition-colors"></i>
                                            </a>

                                            <div class="border-t border-gray-100 my-2"></div>

                                            <a href="<?= $logoutUrl ?>" class="flex items-center space-x-3 px-3 py-3 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 rounded-xl transition-all duration-200 group">
                                                <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-red-100 group-hover:bg-red-200 transition-colors">
                                                    <i class="fas fa-sign-out-alt text-red-600 text-sm"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="font-medium"><?= $__('shared.header.logout') ?></div>
                                                    <div class="text-xs text-red-400">Đăng xuất tài khoản</div>
                                                </div>
                                                <i class="fas fa-chevron-right text-xs text-red-400 group-hover:text-red-500 transition-colors"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Mobile Layout -->
            <div class="md:hidden">
                <!-- Top Row: Logo + Language Switcher -->
                <div class="flex justify-between items-center h-16">
                    <div class="w-10"></div> <!-- Spacer for centering -->
                    <a href="<?= $payDomain ?>" class="flex items-center space-x-3">
                        <img src="<?= $this->asset('images/logo_mtf.png') ?>" alt="MtfGame Logo" class="h-10 w-auto rounded-lg">
                    </a>
                    <!-- Mobile Language Switcher -->
                    <div class="relative group">
                        <button id="languageSwitcherMobile" class="flex items-center space-x-2 px-3 py-2 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 border border-gray-200 hover:border-blue-300 transition-all duration-300 shadow-sm hover:shadow-md" title="<?= $currentLanguage === 'vi' ? 'Đổi ngôn ngữ' : 'Switch Language' ?>">
                            <span class="text-lg">
                                <?= $availableLanguages[$currentLanguage ?? 'vi']['flag'] ?? '🌐' ?>
                            </span>
                            <span class="text-xs font-semibold text-gray-700 group-hover:text-blue-600 transition-colors">
                                <?= strtoupper($currentLanguage ?? 'VI') ?>
                            </span>
                        </button>

                        <!-- Mobile Language Dropdown -->
                        <div id="languageDropdownMobile" class="absolute right-0 mt-3 w-52 bg-white rounded-2xl shadow-2xl border border-gray-100 opacity-0 invisible transform scale-95 translate-y-2 transition-all duration-300 z-50 backdrop-blur-sm">
                            <div class="p-2">
                                <div class="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2 mb-1">
                                    <?= $currentLanguage === 'vi' ? 'Chọn ngôn ngữ' : 'Select Language' ?>
                                </div>
                                <?php foreach ($availableLanguages as $langKey => $language): ?>
                                    <button class="language-option-mobile w-full px-3 py-3 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 flex items-center space-x-3 rounded-xl transition-all duration-200 group <?= $langKey === ($currentLanguage ?? 'vi') ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-sm' : 'text-gray-700' ?>" data-language="<?= $langKey ?>">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-white shadow-sm group-hover:shadow-md transition-shadow">
                                            <span class="text-lg"><?= $language['flag'] ?? '🌐' ?></span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-medium text-sm"><?= $language['name'] ?? ucfirst($langKey) ?></div>
                                            <div class="text-xs text-gray-500"><?= strtoupper($langKey) ?></div>
                                        </div>
                                        <?php if ($langKey === ($currentLanguage ?? 'vi')): ?>
                                            <div class="flex items-center justify-center w-5 h-5 rounded-full bg-blue-100">
                                                <i class="fas fa-check text-xs text-blue-600"></i>
                                            </div>
                                        <?php endif; ?>
                                    </button>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom Row: User Menu (if authenticated) -->
                <?php if ($this->isAuth()): ?>
                <div class="border-t border-gray-200 py-3">
                    <div class="flex items-center justify-between">
                        <!-- Left: Balance + Deposit Button -->
                        <div class="flex items-center space-x-3">
                            <div class="bg-gray-100 px-3 py-1 rounded-full text-xs flex items-center space-x-2">
                                <span class="text-gray-600"><?= $__('shared.header.balance') ?>:</span>
                                <span id="userBalanceMobile" class="text-primary-500 font-semibold">
                                    <?= $this->currency($this->user()['balance'] ?? 0, 'small') ?>
                                </span>
                                <button id="refreshBalanceBtnMobile" class="text-gray-400 hover:text-primary-500 transition-colors p-1 rounded" title="<?= $__('shared.header.refresh_balance') ?>">
                                    <i class="fas fa-sync-alt text-xs"></i>
                                </button>
                            </div>
                            <a href="<?= $depositUrl ?>" class="bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-all shadow-lg flex items-center space-x-2 text-sm font-semibold">
                                <i class="fas fa-gem text-xs"></i>
                                <span><?= $__('shared.header.deposit') ?></span>
                            </a>
                        </div>

                        <!-- Right: User Menu -->
                        <div class="relative">
                            <button id="userMenuButtonMobile" class="flex items-center space-x-2 bg-gray-100 px-3 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                                <div class="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <span class="text-gray-700 text-sm"><?= $this->escape(substr($this->user()['username'] ?? 'User', 0, 8)) ?></span>
                                <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                            </button>
                            <div id="userMenuDropdownMobile" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible transform scale-95 transition-all duration-200 z-50">
                                <a href="<?= $profileUrl ?>" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">
                                    <i class="fas fa-user-cog w-4 text-gray-400"></i>
                                    <span><?= $__('shared.header.profile') ?></span>
                                </a>
                                <a href="<?= $historyUrl ?>" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-scroll w-4 text-gray-400"></i>
                                    <span><?= $__('shared.header.history') ?></span>
                                </a>
                                <div class="border-t border-gray-200"></div>
                                <a href="<?= $logoutUrl ?>" class="flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-b-lg">
                                    <i class="fas fa-sign-out-alt w-4 text-red-400"></i>
                                    <span><?= $__('shared.header.logout') ?></span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="border-t border-gray-200 py-3 text-center">
                    <a href="<?= $loginUrl ?>" class="bg-primary-500 text-white px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors">
                        <?= $__('shared.header.login') ?>
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Banner Section -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
    <div class="relative h-48 sm:h-56 lg:h-64 overflow-hidden rounded-xl shadow-lg">
        <div class="banner-slider relative h-full">
            <!-- Banner 1 -->
            <div class="banner-slide absolute inset-0 bg-primary-500 opacity-100 transition-opacity duration-500 rounded-xl">
                    <div class="w-full px-4 sm:px-6 lg:px-12 h-full">
                        <div class="flex items-center justify-between h-full max-w-none">
                            <div class="text-white flex-1 pr-4">
                                <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-5xl font-bold mb-2 sm:mb-4 leading-tight"><?= $__('shared.banners.special_promotion') ?></h2>
                                <p class="text-sm sm:text-base md:text-lg lg:text-2xl mb-3 sm:mb-6 text-orange-100 max-w-2xl leading-relaxed"><?= $__('shared.banners.bonus_offer') ?></p>
                                <a href="<?= $depositUrl ?>" class="inline-block bg-white text-primary-600 px-4 sm:px-6 lg:px-8 py-2 sm:py-3 rounded-lg sm:rounded-xl font-bold text-sm sm:text-base lg:text-lg hover:bg-gray-100 transition-colors shadow-lg">
                                    <?= $__('shared.banners.deposit_now') ?>
                                </a>
                            </div>
                            <div class="text-white text-4xl sm:text-6xl lg:text-9xl opacity-20 hidden sm:block flex-shrink-0">
                                <i class="fas fa-gift"></i>
                            </div>
                        </div>
                    </div>
                </div>

            <!-- Banner 2 -->
            <div class="banner-slide absolute inset-0 bg-gray-800 opacity-0 transition-opacity duration-500 rounded-xl">
                <div class="w-full px-4 sm:px-6 lg:px-12 h-full">
                    <div class="flex items-center justify-between h-full max-w-none">
                        <div class="text-white flex-1 pr-4">
                            <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-5xl font-bold mb-2 sm:mb-4 leading-tight"><?= $__('shared.banners.new_game_launch') ?></h2>
                            <p class="text-sm sm:text-base md:text-lg lg:text-2xl mb-3 sm:mb-6 text-gray-300 max-w-2xl leading-relaxed"><?= $__('shared.banners.new_game_desc') ?></p>
                            <a href="<?= $homeUrl ?>" class="inline-block bg-primary-500 text-white px-4 sm:px-6 lg:px-8 py-2 sm:py-3 rounded-lg sm:rounded-xl font-bold text-sm sm:text-base lg:text-lg hover:bg-primary-600 transition-colors shadow-lg">
                                <?= $__('shared.banners.play_now') ?>
                            </a>
                        </div>
                        <div class="text-white text-4xl sm:text-6xl lg:text-9xl opacity-20 hidden sm:block flex-shrink-0">
                            <i class="fas fa-dragon"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Banner 3 -->
            <div class="banner-slide absolute inset-0 bg-gray-700 opacity-0 transition-opacity duration-500 rounded-xl">
                <div class="w-full px-4 sm:px-6 lg:px-12 h-full">
                    <div class="flex items-center justify-between h-full max-w-none">
                        <div class="text-white flex-1 pr-4">
                            <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-5xl font-bold mb-2 sm:mb-4 leading-tight"><?= $__('shared.banners.weekly_tournament') ?></h2>
                            <p class="text-sm sm:text-base md:text-lg lg:text-2xl mb-3 sm:mb-6 text-gray-300 max-w-2xl leading-relaxed"><?= $__('shared.banners.tournament_desc') ?></p>
                            <a href="<?= $this->url('/tournament') ?>" class="inline-block bg-primary-500 text-white px-4 sm:px-6 lg:px-8 py-2 sm:py-3 rounded-lg sm:rounded-xl font-bold text-sm sm:text-base lg:text-lg hover:bg-primary-600 transition-colors shadow-lg">
                                <?= $__('shared.banners.join_now') ?>
                            </a>
                        </div>
                        <div class="text-white text-4xl sm:text-6xl lg:text-9xl opacity-20 hidden sm:block flex-shrink-0">
                            <i class="fas fa-trophy"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Banner Navigation -->
        <div class="absolute bottom-3 sm:bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 sm:space-x-3">
            <button class="banner-dot w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-white opacity-100 transition-opacity hover:opacity-80" data-slide="0"></button>
            <button class="banner-dot w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-white opacity-50 transition-opacity hover:opacity-80" data-slide="1"></button>
            <button class="banner-dot w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-white opacity-50 transition-opacity hover:opacity-80" data-slide="2"></button>
        </div>
    </div>
</div>

</header>



<script>
// Banner slider functionality
document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.banner-slide');
    const dots = document.querySelectorAll('.banner-dot');
    let currentSlide = 0;

    function showSlide(index) {
        // Hide all slides
        slides.forEach((slide, i) => {
            if (i === index) {
                slide.style.opacity = '1';
            } else {
                slide.style.opacity = '0';
            }
        });

        // Update dots
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.style.opacity = '1';
            } else {
                dot.style.opacity = '0.5';
            }
        });
    }

    // Auto slide
    setInterval(() => {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }, 5000);

    // Dot navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            currentSlide = index;
            showSlide(currentSlide);
        });
    });

    // User menu dropdown functionality (Desktop)
    const userMenuButton = document.getElementById('userMenuButton');
    const userMenuDropdown = document.getElementById('userMenuDropdown');

    if (userMenuButton && userMenuDropdown) {
        setupUserMenu(userMenuButton, userMenuDropdown);
    }

    // User menu dropdown functionality (Mobile)
    const userMenuButtonMobile = document.getElementById('userMenuButtonMobile');
    const userMenuDropdownMobile = document.getElementById('userMenuDropdownMobile');

    if (userMenuButtonMobile && userMenuDropdownMobile) {
        setupUserMenu(userMenuButtonMobile, userMenuDropdownMobile);
    }

    function setupUserMenu(button, dropdown) {
        let isOpen = false;

        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isOpen) {
                closeDropdown();
            } else {
                openDropdown();
            }
        });

        function openDropdown() {
            dropdown.classList.remove('opacity-0', 'invisible', 'scale-95', 'translate-y-2');
            dropdown.classList.add('opacity-100', 'visible', 'scale-100', 'translate-y-0');
            const chevron = button.querySelector('.fa-chevron-down');
            if (chevron) chevron.style.transform = 'rotate(180deg)';
            isOpen = true;
        }

        function closeDropdown() {
            dropdown.classList.add('opacity-0', 'invisible', 'scale-95', 'translate-y-2');
            dropdown.classList.remove('opacity-100', 'visible', 'scale-100', 'translate-y-0');
            const chevron = button.querySelector('.fa-chevron-down');
            if (chevron) chevron.style.transform = 'rotate(0deg)';
            isOpen = false;
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!button.contains(e.target) && !dropdown.contains(e.target)) {
                closeDropdown();
            }
        });

        // Close dropdown when pressing Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isOpen) {
                closeDropdown();
            }
        });
    }

    // Language switcher functionality (Desktop)
    const languageSwitcher = document.getElementById('languageSwitcher');
    const languageDropdown = document.getElementById('languageDropdown');

    if (languageSwitcher && languageDropdown) {
        setupLanguageMenu(languageSwitcher, languageDropdown, '.language-option');
    }

    // Language switcher functionality (Mobile)
    const languageSwitcherMobile = document.getElementById('languageSwitcherMobile');
    const languageDropdownMobile = document.getElementById('languageDropdownMobile');

    if (languageSwitcherMobile && languageDropdownMobile) {
        setupLanguageMenu(languageSwitcherMobile, languageDropdownMobile, '.language-option-mobile');
    }

    function setupLanguageMenu(button, dropdown, optionSelector) {
        let isOpen = false;

        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isOpen) {
                closeLanguageDropdown();
            } else {
                openLanguageDropdown();
            }
        });

        function openLanguageDropdown() {
            dropdown.classList.remove('opacity-0', 'invisible', 'scale-95', 'translate-y-2');
            dropdown.classList.add('opacity-100', 'visible', 'scale-100', 'translate-y-0');
            isOpen = true;
        }

        function closeLanguageDropdown() {
            dropdown.classList.add('opacity-0', 'invisible', 'scale-95', 'translate-y-2');
            dropdown.classList.remove('opacity-100', 'visible', 'scale-100', 'translate-y-0');
            isOpen = false;
        }

        // Language switching
        dropdown.querySelectorAll(optionSelector).forEach(option => {
            option.addEventListener('click', function() {
                const language = this.dataset.language;
                switchLanguage(language);
                closeLanguageDropdown();
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!button.contains(e.target) && !dropdown.contains(e.target)) {
                closeLanguageDropdown();
            }
        });

        // Close dropdown when pressing Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isOpen) {
                closeLanguageDropdown();
            }
        });
    }

    // Switch language function
    function switchLanguage(language) {
        fetch('/api/settings/language', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'language=' + encodeURIComponent(language)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                GamingApp.showNotification(data.message || 'Language switched successfully', 'success');

                // Reload page to apply language changes
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                GamingApp.showNotification(data.message || 'Failed to switch language', 'error');
            }
        })
        .catch(error => {
            console.error('Language switch error:', error);
            GamingApp.showNotification('Failed to switch language', 'error');
        });
    }
});
</script>
