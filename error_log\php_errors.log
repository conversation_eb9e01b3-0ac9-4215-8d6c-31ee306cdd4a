[30-May-2025 00:37:39 Asia/<PERSON>_<PERSON>_<PERSON>] API Call: {"timestamp":"2025-05-30 00:37:39","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"f094d67ef1842d7db2dc7ffec0afa257a41ff521c1325a5d33b31d83d9e46164","refresh_token":"93fb89496143dec0031d1676af64a35db5686621b1b7787eb0f17f38635f7bd0","expires_at":"2025-06-06 00:37:39"}}},"http_code":200}
[30-May-2025 00:37:40 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 00:37:40","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":false,"message":"Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gmtool.api_rate_limits' doesn't exist"},"http_code":200}
[30-May-2025 00:38:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 00:38:12","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":false,"message":"Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gmtool.api_rate_limits' doesn't exist"},"http_code":200}
[30-May-2025 00:38:12 Asia/Ho_Chi_Minh] Failed to sync user data from API: Query failed: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'gmtool.api_rate_limits' doesn't exist
[30-May-2025 01:12:53 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:12:53","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"0d623012892c97225ac7faaed008604f7246ead795d50b52f0a3b3909790e30c","refresh_token":"1cfb6f03c506e6f3ace3b51d70ddfda6a607f84e7ff5f4a07d080adf9285c284","expires_at":"2025-06-06 01:12:53"}}},"http_code":200}
[30-May-2025 01:12:55 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:12:55","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:12:53"}}},"http_code":200}
[30-May-2025 01:12:55 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 01:19:45 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:19:45","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:12:53"}}},"http_code":200}
[30-May-2025 01:19:45 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 01:19:54 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:19:54","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"8cec67db65118d4bd6decce23f8b89e8c84c3491368ace7ad3e6f51dce8bd205","refresh_token":"fc170512c8eefd523f0872ef1ed1b4080c2f2ed96c3a086c95e6ee1afd3d3f54","expires_at":"2025-06-06 01:19:54"}}},"http_code":200}
[30-May-2025 01:19:55 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:19:55","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:19:54"}}},"http_code":200}
[30-May-2025 01:19:55 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 01:36:38 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:36:38","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:19:54"}}},"http_code":200}
[30-May-2025 01:36:38 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 01:36:54 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:36:54","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:19:54"}}},"http_code":200}
[30-May-2025 01:36:54 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 01:37:24 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:37:24","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:19:54"}}},"http_code":200}
[30-May-2025 01:37:24 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 01:37:35 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 01:37:35","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:19:54"}}},"http_code":200}
[30-May-2025 01:37:35 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 02:37:54 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 02:37:54","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 01:19:54"}}},"http_code":200}
[30-May-2025 02:37:54 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 02:37:59 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 02:37:59","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"081dc00bd0085de5804cf961b4592f923fb94681e62471649c0dfdb1c8029c20","refresh_token":"b901bd80169cb306986331a455cd64d05c487a123f9064d386a35a1d52a702f3","expires_at":"2025-06-06 02:37:59"}}},"http_code":200}
[30-May-2025 02:38:00 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 02:38:00","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 02:37:59"}}},"http_code":200}
[30-May-2025 02:38:00 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 02:38:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 02:38:32","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"f345837d6fe179469ad0ab435545bfcf6ab3d7a156c00440dd3d094da4af9c5c","refresh_token":"bf3ce9b9f42b550130dfc18cfd9e5d619097daa6763a0e9daee232109e827524","expires_at":"2025-06-06 02:38:32"}}},"http_code":200}
[30-May-2025 02:38:33 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 02:38:33","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 02:38:32"}}},"http_code":200}
[30-May-2025 02:38:33 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 03:02:13 Asia/Ho_Chi_Minh] Invalid JSON response: Syntax error
[30-May-2025 03:02:39 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Warning</b>:  json_decode() expects parameter 1 to be string, array given in <b>/var/www/api/Core/Validator.php</b> on line <b>198</b><br />
<br />
<b>Warning</b>:  Cannot modify header information - headers already sent by (output started at /var/www/api/Core/Validator.php:198) in <b>/var/www/app/Core/Controller.php</b> on line <b>110</b><br />
{"status":false,"message":"Tr\u01b0\u1eddng password ph\u1ea3i c\u00f3 \u00edt nh\u1ea5t 8 k\u00fd t\u1ef1, bao g\u1ed3m ch\u1eef hoa, ch\u1eef th\u01b0\u1eddng v\u00e0 s\u1ed1","errors":null}
[30-May-2025 03:06:41 Asia/Ho_Chi_Minh] Invalid JSON response: array(3) {
  ["user_agent"]=>
  string(111) "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  ["ip_address"]=>
  string(12) "************"
  ["platform"]=>
  string(3) "web"
}
<br />
<b>Warning</b>:  json_decode() expects parameter 1 to be string, array given in <b>/var/www/api/Core/Validator.php</b> on line <b>199</b><br />
<br />
<b>Warning</b>:  Cannot modify header information - headers already sent by (output started at /var/www/api/Core/Validator.php:198) in <b>/var/www/app/Core/Controller.php</b> on line <b>110</b><br />
{"status":false,"message":"Tr\u01b0\u1eddng password ph\u1ea3i c\u00f3 \u00edt nh\u1ea5t 8 k\u00fd t\u1ef1, bao g\u1ed3m ch\u1eef hoa, ch\u1eef th\u01b0\u1eddng v\u00e0 s\u1ed1","errors":null}
[30-May-2025 03:11:28 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Warning</b>:  json_decode() expects parameter 1 to be string, array given in <b>/var/www/api/Core/Validator.php</b> on line <b>198</b><br />
<br />
<b>Warning</b>:  Cannot modify header information - headers already sent by (output started at /var/www/api/Core/Validator.php:198) in <b>/var/www/app/Core/Controller.php</b> on line <b>110</b><br />
{"status":false,"message":"Tr\u01b0\u1eddng password ph\u1ea3i c\u00f3 \u00edt nh\u1ea5t 8 k\u00fd t\u1ef1, bao g\u1ed3m ch\u1eef hoa, ch\u1eef th\u01b0\u1eddng v\u00e0 s\u1ed1","errors":null}
[30-May-2025 03:15:42 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Warning</b>:  json_decode() expects parameter 1 to be string, array given in <b>/var/www/api/Core/Validator.php</b> on line <b>198</b><br />
{"status":false,"message":"Trường password phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số","errors":null}
[30-May-2025 03:18:21 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Warning</b>:  json_decode() expects parameter 1 to be string, array given in <b>/var/www/api/Core/Validator.php</b> on line <b>198</b><br />
{"status":false,"message":"Trường password phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số","errors":null}
[30-May-2025 03:18:21 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/Register - Data: Array
[30-May-2025 03:18:44 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Warning</b>:  json_decode() expects parameter 1 to be string, array given in <b>/var/www/api/Core/Validator.php</b> on line <b>198</b><br />
{"status":false,"message":"Trường password phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số","errors":null}
[30-May-2025 03:18:44 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/Register - Data: {"register_type":"account","source":"web","username":"asfd2141246","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}}
[30-May-2025 03:21:47 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:21:47","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asfd2141246","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":false,"message":"Trường password phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số","errors":null},"http_code":200}
[30-May-2025 03:27:03 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:27:03","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asfd2141246","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":false,"message":"Trường password phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số","errors":null},"http_code":200}
[30-May-2025 03:27:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:27:10","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asfd2141246","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838c31ec4d96","username":"asfd2141246","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"e34101747b3df0e8aa0e06ac24a8088782cf52e37dd7eb0dec14c1186ef97fa4","refresh_token":"07481e3651df10a14fc92d7de611d0d914e2d47d1212d4ad01b67536bcbde49c","expires_at":"2025-06-06 03:27:10"}}},"http_code":200}
[30-May-2025 03:27:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:27:12","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838c31ec4d96","username":"asfd2141246","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 03:27:10"}}},"http_code":200}
[30-May-2025 03:27:12 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838c31ec4d96","username":"asfd2141246","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 03:31:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:31:25","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838c31ec4d96","username":"asfd2141246","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 03:27:10"}}},"http_code":200}
[30-May-2025 03:31:25 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838c31ec4d96","username":"asfd2141246","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 03:32:17 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:32:17","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asdasd142124124","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":false,"message":"Trường phone là bắt buộc","errors":null},"http_code":200}
[30-May-2025 03:33:03 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:33:03","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asdasd142124124","password":"asdasd","email":"<EMAIL>","phone":"**********","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":false,"message":"Trường phone phải là số nguyên","errors":null},"http_code":200}
[30-May-2025 03:33:09 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:33:09","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asdasd142124124","password":"asdasd","email":"<EMAIL>","phone":"**********","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":false,"message":"Trường phone phải là số nguyên","errors":null},"http_code":200}
[30-May-2025 03:33:47 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:33:47","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asdasd142124124","password":"asdasd","email":"<EMAIL>","phone":"**********","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838c4ab4eb41","username":"asdasd142124124","email":"<EMAIL>","is_email_verified":false,"phone":"**********","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"5f07e42b60b46edb869f6c285e2503ff3fb755a4d579f57737f13b893c2cf363","refresh_token":"2c21d7476752d3f5a41870793eeb7b5f3bd4970ff3438fc10f3af4b91bb7cb7d","expires_at":"2025-06-06 03:33:47"}}},"http_code":200}
[30-May-2025 03:33:48 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:33:48","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838c4ab4eb41","username":"asdasd142124124","email":"<EMAIL>","is_email_verified":false,"phone":"**********","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 03:33:47"}}},"http_code":200}
[30-May-2025 03:33:48 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838c4ab4eb41","username":"asdasd142124124","email":"<EMAIL>","is_email_verified":false,"phone":"**********","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 03:55:45 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:55:45","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838c4ab4eb41","username":"asdasd142124124","email":"<EMAIL>","is_email_verified":false,"phone":"**********","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 03:33:47"}}},"http_code":200}
[30-May-2025 03:55:45 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838c4ab4eb41","username":"asdasd142124124","email":"<EMAIL>","is_email_verified":false,"phone":"**********","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 03:55:49 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:55:49","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"ec69654c150f691157232e839baa468028c3738d9c1619fa7f1a9d8a7ba50396","refresh_token":"4acb9277b84a6a06d012ba9a3b67a571d834de85a1ded6f79027a920ebedbc82","expires_at":"2025-06-06 03:55:49"}}},"http_code":200}
[30-May-2025 03:55:51 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 03:55:51","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 03:55:49"}}},"http_code":200}
[30-May-2025 03:55:51 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:27:08 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:27:08","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 03:55:49"}}},"http_code":200}
[30-May-2025 04:27:08 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:27:11 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:27:11","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"eb3073ef19f0d948622a6d665427087fb3bc039d87b22be3a94b3557daf70ee1","refresh_token":"88cd2dff9dc50ac3648865bf4c683350e8604809b98996f94ae6b100b2ec856a","expires_at":"2025-06-06 04:27:11"}}},"http_code":200}
[30-May-2025 04:27:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:27:13","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:27:11"}}},"http_code":200}
[30-May-2025 04:27:13 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:27:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:27:16","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:27:11"}}},"http_code":200}
[30-May-2025 04:27:16 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:37:05 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:05","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:27:11"}}},"http_code":200}
[30-May-2025 04:37:05 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:37:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:10","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"menonguvl","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 04:37:19 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:19","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"vailolgame","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"dc359a3d6a248d84734947a6b1702c93445304641e92327fa6ae89d5cd9fdecb","refresh_token":"4d1db0f824cfe48f228ead43fc1a5f64e7e1ec3ae7b0e7906253c7554f2a1aae","expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:37:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:21","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:37:21 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838d38fc314d","username":"vailolgame","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 04:37:31 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:31","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 04:42:31","wait_time":60}},"http_code":200}
[30-May-2025 04:37:39 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:39","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"1241a"},"response":{"status":false,"message":"Trường verification_code phải là số","errors":null},"http_code":200}
[30-May-2025 04:37:49 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:49","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"123123"},"response":{"status":false,"message":"Mã xác thực không hợp lệ hoặc đã hết hạn"},"http_code":200}
[30-May-2025 04:37:54 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:54","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"834457"},"response":{"status":true,"message":"Xác thực email thành công","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 04:37:55 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:37:55","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:37:55 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 04:38:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:15","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/SendPhoneVerification","request_data":{"phone":"**********"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 04:43:15","wait_time":60}},"http_code":200}
[30-May-2025 04:38:19 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:19","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyPhoneOTP","request_data":{"phone":"**********","otp":"45487"},"response":{"status":false,"message":"Mã OTP không hợp lệ hoặc đã hết hạn"},"http_code":200}
[30-May-2025 04:38:24 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:24","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyPhoneOTP","request_data":{"phone":"**********","otp":"11111"},"response":{"status":true,"message":"Xác thực số điện thoại thành công","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"user_id":"account_6838d38fc314d","phone":"**********","is_phone_verified":true}},"http_code":200}
[30-May-2025 04:38:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:25","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:38:25 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:38:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:32","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"ad123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 04:38:36 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:36","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":true,"message":"Đổi mật khẩu thành công","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 04:38:38 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:38","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:38:38 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:38:41 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:41","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:38:41 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 04:38:42 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:42","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:38:48 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 04:38:48","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 04:37:19"}}},"http_code":200}
[30-May-2025 04:38:48 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838d38fc314d","username":"vailolgame","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 05:27:45 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:27:45","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"440c275802c37729229065914fd9cbbbd7dff6f2b8f692cdc38a19c3d4b6de05","refresh_token":"35da71ea899e90a12691605665e479e99ca5c31b3b48c5b23095c99452ff8cb0","expires_at":"2025-06-06 05:27:45"}}},"http_code":200}
[30-May-2025 05:27:47 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:27:47","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:27:45"}}},"http_code":200}
[30-May-2025 05:27:47 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 05:29:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:29:26","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:27:45"}}},"http_code":200}
[30-May-2025 05:29:26 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 05:32:08 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:32:08","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:27:45"}}},"http_code":200}
[30-May-2025 05:32:08 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 05:32:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:32:13","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:27:45"}}},"http_code":200}
[30-May-2025 05:32:13 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 05:33:59 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:33:59","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:27:45"}}},"http_code":200}
[30-May-2025 05:33:59 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 05:34:09 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:34:09","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asd4124124asd","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838e0e160adf","username":"asd4124124asd","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"f0d7496beda2f55c0e41659c7d90d2e0b06dc78ac3333112771f9b246300c75a","refresh_token":"ed7aeab259d2a907cf520034f42e3e0b881f47fc984428b38bf6173c3a42892a","expires_at":"2025-06-06 05:34:09"}}},"http_code":200}
[30-May-2025 05:34:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:34:10","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838e0e160adf","username":"asd4124124asd","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:34:09"}}},"http_code":200}
[30-May-2025 05:34:10 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838e0e160adf","username":"asd4124124asd","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 05:34:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:34:25","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 05:39:25","wait_time":60}},"http_code":200}
[30-May-2025 05:34:32 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:34:32","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"44545"},"response":{"status":false,"message":"Mã xác thực không hợp lệ hoặc đã hết hạn"},"http_code":200}
[30-May-2025 05:34:44 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:34:44","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"498190"},"response":{"status":true,"message":"Xác thực email thành công","data":{"user":{"user_id":"account_6838e0e160adf","username":"asd4124124asd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 05:34:46 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:34:46","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838e0e160adf","username":"asd4124124asd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:34:09"}}},"http_code":200}
[30-May-2025 05:34:46 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838e0e160adf","username":"asd4124124asd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 05:36:43 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:36:43","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":false,"message":"Tài khoản đã bị khóa"},"http_code":200}
[30-May-2025 05:36:43 Asia/Ho_Chi_Minh] Failed to sync user data from API: Tài khoản đã bị khóa
[30-May-2025 05:38:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:38:13","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asf124415244","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838e1d55204b","username":"asf124415244","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"bd759d024adab55f64ffa8a5dfb9279c3583651447f7ed9247d9a4ded9389fa0","refresh_token":"6a2cfa03b4e07cd8ae6d73b5f3537c9947421939fe353cef6f79fe5b99dd14f1","expires_at":"2025-06-06 05:38:13"}}},"http_code":200}
[30-May-2025 05:38:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:38:14","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838e1d55204b","username":"asf124415244","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:38:13"}}},"http_code":200}
[30-May-2025 05:38:14 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838e1d55204b","username":"asf124415244","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 05:38:43 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:38:43","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":false,"message":"Tài khoản đã bị khóa"},"http_code":200}
[30-May-2025 05:38:43 Asia/Ho_Chi_Minh] Failed to sync user data from API: Tài khoản đã bị khóa
[30-May-2025 05:39:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:39:15","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"4bef27f978d8ed925fee2a3bd0183031efc10dff7f482a0c72f4cf0d9c24809c","refresh_token":"5fbfc0be40ec1fa72405be4b6ea8d171bfade36bb2b25ba7c502b697ce22cee9","expires_at":"2025-06-06 05:39:15"}}},"http_code":200}
[30-May-2025 05:39:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 05:39:16","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:39:15"}}},"http_code":200}
[30-May-2025 05:39:16 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:05:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:05:10","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:39:15"}}},"http_code":200}
[30-May-2025 06:05:10 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:05:17 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:05:17","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 05:39:15"}}},"http_code":200}
[30-May-2025 06:05:17 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:05:51 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:05:51","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":false,"message":"Tài khoản đã bị khóa"},"http_code":200}
[30-May-2025 06:05:51 Asia/Ho_Chi_Minh] Failed to sync user data from API: Tài khoản đã bị khóa
[30-May-2025 06:12:53 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/Login - Data: {"login_type":"account","username":"asdasd22","password":"asdasd"}
[30-May-2025 06:12:53 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Fatal error</b>:  Api\Controllers\GameUserController and Api\Core\ApiHandler define the same property ($userService) in the composition of Api\Controllers\GameUserController. However, the definition differs and is considered incompatible. Class was composed in <b>/var/www/api/Controllers/GameUserController.php</b> on line <b>9</b><br />

[30-May-2025 06:12:56 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/Login - Data: {"login_type":"account","username":"asdasd22","password":"123123"}
[30-May-2025 06:12:56 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Fatal error</b>:  Api\Controllers\GameUserController and Api\Core\ApiHandler define the same property ($userService) in the composition of Api\Controllers\GameUserController. However, the definition differs and is considered incompatible. Class was composed in <b>/var/www/api/Controllers/GameUserController.php</b> on line <b>9</b><br />

[30-May-2025 06:12:57 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/Login - Data: {"login_type":"account","username":"asdasd22","password":"asdasd"}
[30-May-2025 06:12:57 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Fatal error</b>:  Api\Controllers\GameUserController and Api\Core\ApiHandler define the same property ($userService) in the composition of Api\Controllers\GameUserController. However, the definition differs and is considered incompatible. Class was composed in <b>/var/www/api/Controllers/GameUserController.php</b> on line <b>9</b><br />

[30-May-2025 06:15:11 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:15:11","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tài khoản đã bị khóa"},"http_code":200}
[30-May-2025 06:15:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:15:13","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tài khoản đã bị khóa"},"http_code":200}
[30-May-2025 06:15:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:15:29","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"cd185be5520f3c9c66b4a705ef8505081071f8a58d9a006e96833494170f9c02","refresh_token":"67171ddf05afa2ed698cf7e12fe3ac52b5121f922ba30f7b68f2723157bfae6e","expires_at":"2025-06-06 06:15:29"}}},"http_code":200}
[30-May-2025 06:15:30 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:15:30","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:15:29"}}},"http_code":200}
[30-May-2025 06:15:30 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:15:42 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:15:42","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:15:29"}}},"http_code":200}
[30-May-2025 06:15:42 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:15:50 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:15:50","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:15:29"}}},"http_code":200}
[30-May-2025 06:15:50 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:16:04 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:16:04","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"662dd0a755f4a898d694e5d80e9f07f5d44fd08b4a0bb0a3f2686ad57daf09fa","refresh_token":"76f8a115fdde0ebd6565859c45d6b7cc6c889688b17ff48d4a1937089242cbd6","expires_at":"2025-06-06 06:16:04"}}},"http_code":200}
[30-May-2025 06:16:06 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:16:06","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:16:04"}}},"http_code":200}
[30-May-2025 06:16:06 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:16:47 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:16:47","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":false,"message":"Tài khoản đã bị khóa"},"http_code":200}
[30-May-2025 06:16:47 Asia/Ho_Chi_Minh] Failed to sync user data from API: Tài khoản đã bị khóa
