[30-May-2025 06:48:30 Asia/<PERSON>_<PERSON>_<PERSON>] API Call: {"timestamp":"2025-05-30 06:48:30","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"dda57f69dee5271895c6885d6686b699b09e8ddaa9e475275b9470c23d841686","refresh_token":"aa20060f11265a8e0c7706cb81c675622dbddd455d46ddaddd021c9a34faa465","expires_at":"2025-06-06 06:48:30"}}},"http_code":200}
[30-May-2025 06:48:31 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/VerifyToken - Data: []
[30-May-2025 06:48:31 Asia/Ho_Chi_Minh] Invalid JSON response: string(940) "{"data":{"id":"1","user_id":"account_6830eb6bcf70d","username":"asdasd22","password":"$2y$10$NTvbCPOYsirw2EBp6hz\/6uC19hFyGidxhp7PhIWMoB1EmkXigH3R.","email":"<EMAIL>","phone":"**********","full_name":null,"address":null,"date_of_birth":null,"id_number":null,"id_issue_date":null,"id_issue_place":null,"gender":"male","is_email_verified":"1","is_phone_verified":"1","oauth_id":null,"is_gm":"0","register_source":"web","register_info":"{\"ip\": \"**********\", \"device_id\": null, \"device_info\": {\"platform\": \"web\", \"ip_address\": \"**********\", \"user_agent\": \"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36\"}}","user_type":"account","last_login":"2025-05-30 06:24:20","last_login_servers":null,"balance":"0","status":"1","notes":"","created_at":"2025-05-24 04:40:59","updated_at":"2025-05-30 06:24:20"},"cached_at":**********,"expires_at":**********}"
{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:48:30"}}}
[30-May-2025 06:48:31 Asia/Ho_Chi_Minh] Failed to sync user data from API: Invalid JSON response: No error
[30-May-2025 06:51:41 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:51:41","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"1217586c3e87026c9a4ca53d5c7fa65a4abb7b0ebb08597734a0fd1638aae939","refresh_token":"aad4ca840a92084e12e4c7d7a0c009845965f81f605658bc8c49ef5cc81f8437","expires_at":"2025-06-06 06:51:41"}}},"http_code":200}
[30-May-2025 06:51:42 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/VerifyToken - Data: []headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36)","Authorization: Bearer 1217586c3e87026c9a4ca53d5c7fa65a4abb7b0ebb08597734a0fd1638aae939"]
[30-May-2025 06:51:42 Asia/Ho_Chi_Minh] Invalid JSON response: string(940) "{"data":{"id":"1","user_id":"account_6830eb6bcf70d","username":"asdasd22","password":"$2y$10$NTvbCPOYsirw2EBp6hz\/6uC19hFyGidxhp7PhIWMoB1EmkXigH3R.","email":"<EMAIL>","phone":"**********","full_name":null,"address":null,"date_of_birth":null,"id_number":null,"id_issue_date":null,"id_issue_place":null,"gender":"male","is_email_verified":"1","is_phone_verified":"1","oauth_id":null,"is_gm":"0","register_source":"web","register_info":"{\"ip\": \"**********\", \"device_id\": null, \"device_info\": {\"platform\": \"web\", \"ip_address\": \"**********\", \"user_agent\": \"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36\"}}","user_type":"account","last_login":"2025-05-30 06:24:20","last_login_servers":null,"balance":"0","status":"1","notes":"","created_at":"2025-05-24 04:40:59","updated_at":"2025-05-30 06:24:20"},"cached_at":**********,"expires_at":**********}"
{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:51:41"}}}
[30-May-2025 06:51:42 Asia/Ho_Chi_Minh] Failed to sync user data from API: Invalid JSON response: No error
[30-May-2025 06:55:35 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:35","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 06:55:38 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:38","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"ef234ff8df447129d50452bc4e964126b2488f1fc7a9f84f72c157d9a43ce962","refresh_token":"e61e9a77a1d3b5680fdde24ca1cafe86e3631946d0b049775917fd801c9ffeb6","expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:55:39 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:39","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:55:39 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:55:41 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:41","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:55:41 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:55:44 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:44","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:55:44 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:55:46 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:46","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:55:46 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:55:49 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:49","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:55:49 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:55:55 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:55","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd22","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:55:59 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:55:59","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:56:01 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:01","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:56:01 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:56:07 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:07","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd22","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:10","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:14","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:16","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd22","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:20 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:20","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd22","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:25","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:56:27 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:27","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:56:27 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:56:31 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:31","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu mới không được trùng với mật khẩu hiện tại"},"http_code":200}
[30-May-2025 06:56:34 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:34","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:38 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:38","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:56:40 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:40","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:56:40 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:56:44 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:44","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:48 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:48","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:56:52 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:52","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:56:54 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:54","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:56:54 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:56:58 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:56:58","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:57:00 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:00","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:57:00 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:57:06 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:06","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:57:08 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:08","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:57:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:10","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:55:38"}}},"http_code":200}
[30-May-2025 06:57:10 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:57:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:13","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 06:57:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:15","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 06:57:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:18","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 06:57:20 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:20","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"3b371fa8f92888f6bb594180338a5f5e85fa513ae448850938b2243e63fdc0e4","refresh_token":"787155c952b651af902d26efce79ed1f9f4d5b5efb3ecbd6345539df3e2d93a2","expires_at":"2025-06-06 06:57:20"}}},"http_code":200}
[30-May-2025 06:57:21 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:21","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:57:20"}}},"http_code":200}
[30-May-2025 06:57:21 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:57:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:26","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:57:29 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:29","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 06:57:33 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:33","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:57:35 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:35","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:57:20"}}},"http_code":200}
[30-May-2025 06:57:35 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:57:39 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:57:39","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 06:58:16 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:58:16","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 06:58:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:58:18","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"d7562a2eb4f543ad7c4992b6276cb848c567455824f13f039e00322173609a96","refresh_token":"8bf3a0f773590f0136223566653671726af225e747faac9cc66717eb2605f232","expires_at":"2025-06-06 06:58:18"}}},"http_code":200}
[30-May-2025 06:58:19 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:58:19","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:58:18"}}},"http_code":200}
[30-May-2025 06:58:19 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:58:24 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:58:24","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 06:58:27 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:58:27","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 06:58:18"}}},"http_code":200}
[30-May-2025 06:58:27 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 06:58:36 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 06:58:36","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 07:14:44 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:14:44","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"671cf977519eb80059549a8707e6ac45e913965c98cba53cd8087782f531aab7","refresh_token":"216ab92f70e4f6098ad519a0c6348c923ba31ea41fc79e4318e64e0c41affffe","expires_at":"2025-06-06 07:14:44"}}},"http_code":200}
[30-May-2025 07:14:46 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:14:46","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:14:44"}}},"http_code":200}
[30-May-2025 07:14:46 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:14:56 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:14:56","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:14:44"}}},"http_code":200}
[30-May-2025 07:14:56 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:14:59 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:14:59","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd22","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 07:15:03 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:15:03","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":false,"message":"Mật khẩu hiện tại không đúng"},"http_code":200}
[30-May-2025 07:15:06 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:15:06","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 07:15:08 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:15:08","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:14:44"}}},"http_code":200}
[30-May-2025 07:15:08 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:15:13 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:15:13","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 07:15:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:15:15","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 07:15:17 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:15:17","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"cb7372aa6542d33772cf0cc175b2a8f56912e1c2de3dde219728afd4226a1c68","refresh_token":"ceb82aae110dab571f047846d8fa35f89b14b7797c46f6ed034c170dfa79dbf0","expires_at":"2025-06-06 07:15:17"}}},"http_code":200}
[30-May-2025 07:15:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:15:18","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:15:17"}}},"http_code":200}
[30-May-2025 07:15:18 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:18:24 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:24","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 07:18:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:26","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"19191dcd33d787b93713a16a241e98c23a47a80842a88a155f4dc0c4ccc7215b","refresh_token":"9b7df2ae1f3acb00b0b8fa13729f7a645f286f26f52253bbe68f1564df3f4f14","expires_at":"2025-06-06 07:18:26"}}},"http_code":200}
[30-May-2025 07:18:27 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:27","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:18:26"}}},"http_code":200}
[30-May-2025 07:18:27 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:18:42 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:42","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 07:18:45 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:45","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:18:26"}}},"http_code":200}
[30-May-2025 07:18:45 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:18:55 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:55","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"aa55c46693145729506e26f7949976015122778871b1b10140f116448df9a066","refresh_token":"65f5b1c62c47d557d83cabd2c5b115e80a2c02f30b448a13548b7a1bd7d36705","expires_at":"2025-06-06 07:18:55"}}},"http_code":200}
[30-May-2025 07:18:56 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:56","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:18:55"}}},"http_code":200}
[30-May-2025 07:18:56 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:18:59 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:18:59","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:18:55"}}},"http_code":200}
[30-May-2025 07:18:59 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:19:08 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:19:08","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:18:55"}}},"http_code":200}
[30-May-2025 07:19:08 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:19:12 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:19:12","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"asdasd","new_password":"123123","confirm_password":"123123"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 07:19:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:19:15","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:18:55"}}},"http_code":200}
[30-May-2025 07:19:15 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:19:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:19:22","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 07:19:24 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:19:24","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"59a56227b95e3bc3a206bee0ca7312705ccb691f2aee5bae33e38cf54b9091fc","refresh_token":"17a7fd074f5228c1c3f82d2172bc5fe6202f48ecf9bbe11ce0dad0c7340ea5d3","expires_at":"2025-06-06 07:19:24"}}},"http_code":200}
[30-May-2025 07:19:25 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:19:25","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:19:24"}}},"http_code":200}
[30-May-2025 07:19:25 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:20:10 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:20:10","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:19:24"}}},"http_code":200}
[30-May-2025 07:20:10 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 07:20:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:20:22","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asdasd","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838f9c6ca277","username":"asdasd","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"bb695ff172996993f7e1317cec5c13f34f67486f05c8ae574ec0277be9292ef1","refresh_token":"f0e0da76d1231528f524888cf5ee5dd7f5868b20ff6b687c5cbd2f3a8d3f4845","expires_at":"2025-06-06 07:20:22"}}},"http_code":200}
[30-May-2025 07:20:24 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:20:24","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838f9c6ca277","username":"asdasd","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:20:22"}}},"http_code":200}
[30-May-2025 07:20:24 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838f9c6ca277","username":"asdasd","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 07:20:31 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:20:31","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 07:25:31","wait_time":60}},"http_code":200}
[30-May-2025 07:20:38 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:20:38","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"622140"},"response":{"status":true,"message":"Xác thực email thành công","data":{"user":{"user_id":"account_6838f9c6ca277","username":"asdasd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 07:20:40 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:20:40","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838f9c6ca277","username":"asdasd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:20:22"}}},"http_code":200}
[30-May-2025 07:20:40 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838f9c6ca277","username":"asdasd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 07:20:45 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:20:45","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838f9c6ca277","username":"asdasd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:20:22"}}},"http_code":200}
[30-May-2025 07:20:45 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838f9c6ca277","username":"asdasd","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 07:38:41 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:38:41","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asfas4fa4sf4","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838fe118b79a","username":"asfas4fa4sf4","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"8edccce5dd5eed81bf222ccc634a382edb3017248f6352002bde35c18590aaf2","refresh_token":"513165e31f14c1c228bac60ac1495d3f5eacf42c5d8a03f03df900efecc8b89f","expires_at":"2025-06-06 07:38:41"}}},"http_code":200}
[30-May-2025 07:38:42 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:38:42","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838fe118b79a","username":"asfas4fa4sf4","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:38:41"}}},"http_code":200}
[30-May-2025 07:38:42 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838fe118b79a","username":"asfas4fa4sf4","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 07:38:55 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:38:55","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":true,"message":"Mã xác thực đã được gửi","data":{"expires_at":"2025-05-30 07:43:55","wait_time":60}},"http_code":200}
[30-May-2025 07:39:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:39:22","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/VerifyEmail","request_data":{"email":"<EMAIL>","verification_code":"554187"},"response":{"status":true,"message":"Xác thực email thành công","data":{"user":{"user_id":"account_6838fe118b79a","username":"asfas4fa4sf4","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 07:39:23 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:39:23","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838fe118b79a","username":"asfas4fa4sf4","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:38:41"}}},"http_code":200}
[30-May-2025 07:39:23 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838fe118b79a","username":"asfas4fa4sf4","email":"<EMAIL>","is_email_verified":true,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 07:41:14 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:41:14","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Register","request_data":{"register_type":"account","source":"web","username":"asfa2fasf2","password":"asdasd","email":"","phone":"","device_info":{"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","ip_address":"************","platform":"web"}},"response":{"status":true,"message":"Registration successful","data":{"user":{"user_id":"account_6838feaa87bc1","username":"asfa2fasf2","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"access_token":"4f90eca6183fa2135ef9de1479426cabb5372f5315bb847f6b6d619ef9ddadbc","refresh_token":"d9ff1490087b6113be65619466c4676d3828614d298a256919a09740ff2f5f7d","expires_at":"2025-06-06 07:41:14"}}},"http_code":200}
[30-May-2025 07:41:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:41:15","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6838feaa87bc1","username":"asfa2fasf2","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 07:41:14"}}},"http_code":200}
[30-May-2025 07:41:15 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6838feaa87bc1","username":"asfa2fasf2","email":"","is_email_verified":false,"phone":"","is_phone_verified":false,"user_type":"account","balance":0}
[30-May-2025 07:41:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 07:41:18","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/SendEmailVerification","request_data":{"email":"<EMAIL>"},"response":{"status":false,"message":"Email đã được sử dụng bởi tài khoản khác"},"http_code":200}
[30-May-2025 14:29:15 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:15","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 14:29:18 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:18","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 14:29:20 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:20","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":false,"message":"Tên tài khoản hoặc mật khẩu không hợp lệ"},"http_code":200}
[30-May-2025 14:29:22 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:22","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"123123"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"2c9644e3400b9858d0a38f25d63084925afc99656233f3eb021b039523f12077","refresh_token":"76603880e5853ff6a581119efa745f564857b38185627027cf9d0d2425a34dcf","expires_at":"2025-06-06 14:29:22"}}},"http_code":200}
[30-May-2025 14:29:26 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:26","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:29:22"}}},"http_code":200}
[30-May-2025 14:29:26 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:29:37 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:37","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/ChangePassword","request_data":{"current_password":"123123","new_password":"asdasd","confirm_password":"asdasd"},"response":{"status":true,"message":"Đổi mật khẩu thành công. Vui lòng đăng nhập lại.","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}}},"http_code":200}
[30-May-2025 14:29:43 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:43","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"314eeced226273b6f607c137e3de2240bf0a16fd4bb771d68dc1754beaef4ef1","refresh_token":"619acebac0d9747d8414c0c04937243fdf6b3e2c15345c8358a9fc438c539e9d","expires_at":"2025-06-06 14:29:43"}}},"http_code":200}
[30-May-2025 14:29:44 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:29:44","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:29:43"}}},"http_code":200}
[30-May-2025 14:29:44 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:31:35 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:31:35","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:29:43"}}},"http_code":200}
[30-May-2025 14:31:35 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:35:45 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:35:45","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:29:43"}}},"http_code":200}
[30-May-2025 14:35:45 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:35:48 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:35:48","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:29:43"}}},"http_code":200}
[30-May-2025 14:35:48 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:36:04 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:36:04","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:29:43"}}},"http_code":200}
[30-May-2025 14:36:04 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:38:46 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/Login - Data: {"login_type":"account","username":"asdasdasd","password":"asdsad"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36)"]
[30-May-2025 14:38:46 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Fatal error</b>:  Uncaught TypeError: Return value of App\Services\UserService::fetchUser() must be of the type array or null, bool returned in /var/www/app/Services/UserService.php:156
Stack trace:
#0 /var/www/app/Services/UserService.php(196): App\Services\UserService-&gt;fetchUser('username', 'asdasdasd')
#1 /var/www/api/Controllers/GameUserController.php(266): App\Services\UserService-&gt;getByUsername('asdasdasd')
#2 /var/www/api/Core/ApiHandler.php(206): Api\Controllers\GameUserController-&gt;Api\Controllers\{closure}(Object(Api\Core\Validator))
#3 /var/www/api/Core/ApiHandler.php(88): Api\Controllers\GameUserController-&gt;Api\Core\{closure}()
#4 /var/www/api/Core/ApiHandler.php(187): Api\Controllers\GameUserController-&gt;handleExceptions(Object(Closure), Array)
#5 /var/www/api/Controllers/GameUserController.php(333): Api\Controllers\GameUserController-&gt;apiEndpoint('login', Object(Closure))
#6 /var/www/api/index.php(81): Api\Controllers\GameUserController-&gt;login()
#7 {main}
  thrown in <b>/var/www/app/Services/UserService.php</b> on line <b>156</b><br />

[30-May-2025 14:38:50 Asia/Ho_Chi_Minh] URL: http://***********:81/api/GameUser/Login - Data: {"login_type":"account","username":"asdasdasd","password":"asdsadasd"}headers: ["Content-Type: application\/json","Accept: application\/json","User-Agent: AFKSDK\/1.0.0 (Web Client; Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36)"]
[30-May-2025 14:38:50 Asia/Ho_Chi_Minh] Invalid JSON response: <br />
<b>Fatal error</b>:  Uncaught TypeError: Return value of App\Services\UserService::fetchUser() must be of the type array or null, bool returned in /var/www/app/Services/UserService.php:156
Stack trace:
#0 /var/www/app/Services/UserService.php(196): App\Services\UserService-&gt;fetchUser('username', 'asdasdasd')
#1 /var/www/api/Controllers/GameUserController.php(266): App\Services\UserService-&gt;getByUsername('asdasdasd')
#2 /var/www/api/Core/ApiHandler.php(206): Api\Controllers\GameUserController-&gt;Api\Controllers\{closure}(Object(Api\Core\Validator))
#3 /var/www/api/Core/ApiHandler.php(88): Api\Controllers\GameUserController-&gt;Api\Core\{closure}()
#4 /var/www/api/Core/ApiHandler.php(187): Api\Controllers\GameUserController-&gt;handleExceptions(Object(Closure), Array)
#5 /var/www/api/Controllers/GameUserController.php(333): Api\Controllers\GameUserController-&gt;apiEndpoint('login', Object(Closure))
#6 /var/www/api/index.php(81): Api\Controllers\GameUserController-&gt;login()
#7 {main}
  thrown in <b>/var/www/app/Services/UserService.php</b> on line <b>156</b><br />

[30-May-2025 14:44:24 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:44:24","method":"POST","url":"http:\/\/***********:81\/api\/GameUser\/Login","request_data":{"login_type":"account","username":"asdasd22","password":"asdasd"},"response":{"status":true,"message":"Login successful","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"access_token":"de51dde1844c86c51edd2e3cd58428af2c271eee0133d2ebd6952643c22badda","refresh_token":"ba98816215d4c7382803bd2fa400e9adea68829cfc73bc95cf9269d9dde7cd9c","expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:44:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:44:28","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:44:28 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:44:28 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:44:28","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:48:55 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:48:55","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:48:55 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:49:02 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:02","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:02 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:49:02 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:02","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:05 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:05","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:05 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:49:09 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:09","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:09 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:49:11 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:11","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:11 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:49:11 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:11","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:17 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:17","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:17 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:49:17 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:17","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:43 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:43","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
[30-May-2025 14:49:43 Asia/Ho_Chi_Minh] User data synced from API: {"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0}
[30-May-2025 14:49:43 Asia/Ho_Chi_Minh] API Call: {"timestamp":"2025-05-30 14:49:43","method":"GET","url":"http:\/\/***********:81\/api\/GameUser\/VerifyToken","request_data":[],"response":{"status":true,"message":"Token hợp lệ","data":{"user":{"user_id":"account_6830eb6bcf70d","username":"asdasd22","email":"<EMAIL>","is_email_verified":true,"phone":"**********","is_phone_verified":true,"user_type":"account","balance":0},"token":{"expires_at":"2025-06-06 14:44:24"}}},"http_code":200}
