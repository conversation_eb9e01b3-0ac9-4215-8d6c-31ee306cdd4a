<?php

namespace Core;

/**
 * Base Model Class
 * Handles API communication and data management
 */
abstract class Model
{
    protected string $baseUrl;
    protected array $headers;
    protected int $timeout;

    public function __construct()
    {
        $app = Application::getInstance();
        $this->baseUrl = $app->getConfig('api.base_url');
        $this->timeout = $app->getConfig('api.timeout') ?? 30;
        $this->headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: ' . $this->getUserAgent()
        ];
    }

    /**
     * Make HTTP GET request
     */
    protected function get(string $endpoint, array $params = []): array
    {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');

        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $this->makeRequest('GET', $url);
    }

    /**
     * Make HTTP POST request
     */
    protected function post(string $endpoint, array $data = []): array
    {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        return $this->makeRequest('POST', $url, $data);
    }

    /**
     * Make HTTP PUT request
     */
    protected function put(string $endpoint, array $data = []): array
    {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        return $this->makeRequest('PUT', $url, $data);
    }

    /**
     * Make HTTP DELETE request
     */
    protected function delete(string $endpoint): array
    {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        return $this->makeRequest('DELETE', $url);
    }

    /**
     * Make HTTP request using cURL
     */
    private function makeRequest(string $method, string $url, array $data = []): array
    {
        $curl = curl_init();

        // Basic cURL options
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $this->headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);

        // Add data for POST/PUT requests
        if (in_array($method, ['POST', 'PUT']) && !empty($data)) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);

        curl_close($curl);

        if ($error) {
            throw new \Exception("cURL Error: {$error}");
        }


        $decodedResponse = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("URL: {$url} - Data: ". JSON_encode(  $data));   
            error_log("Invalid JSON response: ". $response);         
            throw new \Exception("Invalid JSON response: " . json_last_error_msg());
        }

        // Log API calls in debug mode
        $this->logApiCall($method, $url, $data, $decodedResponse, $httpCode);

        return $decodedResponse;
    }

    /**
     * Set authorization header
     */
    protected function setAuthToken(string $token): void
    {
        $this->headers = array_filter($this->headers, function($header) {
            return !str_starts_with($header, 'Authorization:');
        });

        $this->headers[] = "Authorization: Bearer {$token}";
    }

    /**
     * Remove authorization header
     */
    protected function removeAuthToken(): void
    {
        $this->headers = array_filter($this->headers, function($header) {
            return !str_starts_with($header, 'Authorization:');
        });
    }

    /**
     * Log API calls for debugging
     */
    private function logApiCall(string $method, string $url, array $data, array $response, int $httpCode): void
    {
        $app = Application::getInstance();

        if (!$app->getConfig('app.debug')) {
            return;
        }

        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $method,
            'url' => $url,
            'request_data' => $data,
            'response' => $response,
            'http_code' => $httpCode
        ];

        error_log("API Call: " . json_encode($logData, JSON_UNESCAPED_UNICODE));
    }

    /**
     * Handle API response
     */
    protected function handleResponse(array $response): array
    {
        if (!isset($response['status'])) {
            throw new \Exception("Invalid API response format");
        }

        if (!$response['status']) {
            throw new ApiException($response['message'] ?? 'API Error', $response);
        }

        return $response['data'] ?? [];
    }

    /**
     * Validate required fields
     */
    protected function validateRequired(array $data, array $required): void
    {
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new \Exception("Required field '{$field}' is missing");
            }
        }
    }

    /**
     * Get User-Agent string for API requests
     */
    private function getUserAgent(): string
    {
        $app = Application::getInstance();
        $appName = $app->getConfig('app.name') ?? 'Gaming Account Manager';
        $appVersion = $app->getConfig('app.version') ?? '1.0.0';

        // Get client User-Agent if available
        $clientUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

        // Create custom User-Agent string
        return "{$appName}/{$appVersion} (Web Client; {$clientUserAgent})";
    }
}

/**
 * API Exception Class
 */
class ApiException extends \Exception
{
    private array $response;

    public function __construct(string $message, array $response = [])
    {
        $this->response = $response;
        parent::__construct($message);
    }

    public function getResponse(): array
    {
        return $this->response;
    }
}
