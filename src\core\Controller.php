<?php

namespace Core;

use Shared\Helpers\DomainHelper;
use Shared\Helpers\SecurityHelper;
use Shared\Helpers\SeoHelper;
use Shared\Helpers\LanguageHelper;

/**
 * Base Controller Class
 * All controllers should extend this class
 */
abstract class Controller
{
    protected View $view;
    protected Application $app;
    protected DomainHelper $domainHelper;
    protected SecurityHelper $securityHelper;
    protected SeoHelper $seoHelper;
    protected LanguageHelper $languageHelper;

    public function __construct()
    {
        $this->app = Application::getInstance();
        $this->view = $this->app->getView();

        // Initialize helpers using singleton pattern
        $this->domainHelper = DomainHelper::getInstance();
        $this->securityHelper = SecurityHelper::getInstance();

        // Get app name from config for SeoHelper
        $appName = $this->app->getConfig()['app']['name'] ?? 'MtfGame';

        $this->seoHelper = SeoHelper::getInstance($appName);

        // Initialize language helper
        $config = $this->app->getConfig();
        $currentLanguage = $_SESSION['language'] ?? $config['app']['locale'] ?? 'vi';
        $this->languageHelper = LanguageHelper::getInstance($currentLanguage);


    }

    /**
     * Get domain URL using DomainHelper
     */
    protected function getDomainUrl(string $domain): string
    {
        return $this->domainHelper->getDomain($domain);
    }

    /**
     * Get API URL using DomainHelper
     */
    protected function getApiUrl(string $endpoint): string
    {
        return $this->domainHelper->getApiUrl($endpoint);
    }

    /**
     * Get page URL using DomainHelper
     */
    protected function getPageUrl(string $page, ?string $domain = null): string
    {
        return $this->domainHelper->getPageUrl($page, $domain);
    }

    /**
     * Get current domain
     */
    protected function getCurrentDomain(): string
    {
        return $this->domainHelper->getCurrentDomain();
    }

    /**
     * Get home URL for current domain
     */
    protected function getHomeUrl(): string
    {
        return $this->domainHelper->getHomeUrl();
    }

    /**
     * Get login URL with return parameter
     */
    protected function getLoginUrl(?string $returnUrl = null): string
    {
        return $this->domainHelper->getLoginUrl($returnUrl);
    }

    /**
     * Get logout URL with return parameter
     */
    protected function getLogoutUrl(?string $returnUrl = null): string
    {
        return $this->domainHelper->getLogoutUrl($returnUrl);
    }

    /**
     * Render view with data
     */
    protected function render(string $template, array $data = []): void
    {
        // Add domain URLs to all views automatically
        $data = array_merge($data, $this->getDomainData());

        // Add theme and language data to all views
        $data = array_merge($data, $this->getThemeData());

        $this->view->render($template, $data);
    }

    /**
     * Get domain data for views
     */
    protected function getDomainData(): array
    {
        $currentDomain = $this->getCurrentDomain();
        $homeUrl = $this->getHomeUrl();
        $currentUrl = $_SERVER['REQUEST_URI'] ?? '/';
        $fullCurrentUrl = $this->getDomainUrl($currentDomain) . $currentUrl;

        return [
            'domainData' => [
                'currentDomain' => $currentDomain,
                'homeUrl' => $homeUrl,
                'loginUrl' => ($currentDomain === 'pay') ? $this->getLoginUrl($fullCurrentUrl) : $this->getApiUrl('auth.login'),
                'depositUrl' => $this->getPageUrl('deposit'),
                'profileUrl' => $this->getPageUrl('profile'),
                'historyUrl' => $this->getPageUrl('history'),
                'logoutUrl' => $this->getLogoutUrl($homeUrl),
                'domains' => [
                    'id' => $this->getDomainUrl('id'),
                    'pay' => $this->getDomainUrl('pay')
                ]
            ]
        ];
    }

    /**
     * Get language data for views
     */
    protected function getThemeData(): array
    {
        return [
            'currentLanguage' => $this->languageHelper->getCurrentLanguage(),
            'availableLanguages' => $this->app->getConfig()['ui']['languages'] ?? [],
            '__' => function($key, $params = []) {
                return $this->languageHelper->translate($key, $params);
            }
        ];
    }

    /**
     * Return JSON response
     */
    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Return clean JSON response without any output contamination
     */
    protected function jsonResponse(array $data, int $statusCode = 200): void
    {
        // Clean any output buffer
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Set headers
        http_response_code($statusCode);
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');
        header('Pragma: no-cache');

        // Output clean JSON and exit
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * Redirect to URL
     */
    protected function redirect(string $url, int $statusCode = 302): void
    {
        $this->app->redirect($url, $statusCode);
    }

    /**
     * Get request input
     */
    protected function input(string $key = null, $default = null)
    {
        if ($key === null) {
            return array_merge($_GET, $_POST);
        }

        return $_POST[$key] ?? $_GET[$key] ?? $default;
    }

    /**
     * Validate request input
     */
    protected function validate(array $rules): array
    {
        $errors = [];
        $data = [];

        foreach ($rules as $field => $rule) {
            $value = $this->input($field);
            $data[$field] = $value;

            if (is_string($rule)) {
                $rule = explode('|', $rule);
            }

            foreach ($rule as $r) {
                if ($r === 'required' && empty($value)) {
                    $errors[$field][] = "Trường {$field} là bắt buộc";
                } elseif (str_starts_with($r, 'min:')) {
                    $min = (int) substr($r, 4);
                    if (!empty($value) && strlen($value) < $min) {
                        $errors[$field][] = "Trường {$field} phải có ít nhất {$min} ký tự";
                    }
                } elseif (str_starts_with($r, 'max:')) {
                    $max = (int) substr($r, 4);
                    if (!empty($value) && strlen($value) > $max) {
                        $errors[$field][] = "Trường {$field} không được vượt quá {$max} ký tự";
                    }
                } elseif ($r === 'email' && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field][] = "Trường {$field} phải là email hợp lệ";
                } elseif ($r === 'phone' && !empty($value) && !preg_match('/^0[0-9]{9}$/', $value)) {
                    $errors[$field][] = "Trường {$field} phải là số điện thoại hợp lệ";
                } elseif ($field === 'username' && !empty($value)) {
                    // Check for whitespace in username
                    if (strpos($value, ' ') !== false) {
                        $errors[$field][] = "Tên đăng nhập không được chứa khoảng trắng";
                    } elseif (!preg_match('/^[a-zA-Z0-9_]{6,20}$/', $value)) {
                        $errors[$field][] = "Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới";
                    }
                }
            }
        }

        if (!empty($errors)) {
            throw new ValidationException($errors);
        }

        return $data;
    }

    /**
     * Get current user from session with auto-sync from API
     */
    protected function getUser(): ?array
    {
        // If user is authenticated, sync data from API
        if ($this->isAuthenticated() && isset($_SESSION['user'])) {
            $this->syncUserFromApi();
        }

        return $_SESSION['user'] ?? null;
    }

    /**
     * Sync user data from API on every page load
     */
    private function syncUserFromApi(): void
    {
        try {
            // Check if we need to sync (avoid too frequent calls)
            $lastSync = $_SESSION['last_user_sync'] ?? 0;
            $syncInterval = 3; // 3 seconds minimum between syncs

            if ((time() - $lastSync) < $syncInterval) {
                return; // Skip sync if too recent
            }

            $token = $this->getAccessToken();
            if (!$token) {
                return;
            }

            // Use ApiService to verify token and get fresh user data
            $apiService = new \Shared\Services\ApiService();
            $result = $apiService->verifyToken($token);

            if (isset($result['user'])) {
                // Update session with fresh user data
                $this->setUserSession($result['user'], $token);
                $_SESSION['last_user_sync'] = time();

                error_log('User data synced from API: ' . json_encode($result['user']));
            }else{
                $this->clearUserSession();
            }

        } catch (\Exception $e) {
            // Log error but don't break the page
            echo 123124124124124124124;
            exit;
            error_log('Failed to sync user data from API: ' . $e->getMessage());

            // If token is invalid, clear session
            $this->clearUserSession();
        }
    }

    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated(): bool
    {
        return isset($_SESSION['user']) && isset($_SESSION['access_token']);
    }

    /**
     * Set flash message
     */
    protected function setFlash(string $type, string $message): void
    {
        $_SESSION['flash'][$type] = $message;
    }

    /**
     * Get and clear flash messages
     */
    protected function getFlash(): array
    {
        $flash = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $flash;
    }

    /**
     * Set user session
     */
    protected function setUserSession(array $user, string $token): void
    {
        // Ensure balance is properly formatted as float
        if (isset($user['balance'])) {
            $user['balance'] = (float)$user['balance'];
        }

        $_SESSION['user'] = $user;
        $_SESSION['access_token'] = $token;
        $_SESSION['last_activity'] = time();
        $_SESSION['last_token_check'] = time();
    }

    /**
     * Clear user session
     */
    protected function clearUserSession(): void
    {
        unset($_SESSION['user']);
        unset($_SESSION['access_token']);
        unset($_SESSION['last_activity']);
        unset($_SESSION['last_token_check']);
    }

    /**
     * Get access token from session
     */
    protected function getAccessToken(): ?string
    {
        return $_SESSION['access_token'] ?? null;
    }

    /**
     * Get real IP address (handles proxy/load balancer/Docker)
     */
    protected function getRealIpAddress(): string
    {
        // Check for various headers that might contain the real IP
        $headers = [
            'HTTP_X_FORWARDED_FOR',     // Most common proxy header
            'HTTP_X_REAL_IP',           // Nginx real_ip_header
            'HTTP_CLIENT_IP',           // Proxy header
            'HTTP_X_CLUSTER_CLIENT_IP', // Cluster header
            'HTTP_FORWARDED_FOR',       // RFC 7239
            'HTTP_FORWARDED',           // RFC 7239
            'REMOTE_ADDR'               // Fallback to standard
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]); // Get first IP if multiple

                // Validate IP and exclude private/reserved ranges for proxies
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }

                // If validation fails but it's a valid IP format, still use it
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }

        // Fallback to REMOTE_ADDR if nothing else works
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Static method to sync user session (for use in middleware)
     */
    public static function syncUserSessionData(array $apiUserData): void
    {
        $currentUser = $_SESSION['user'] ?? [];

        // Update user session with fresh data from API
        $_SESSION['user'] = [
            'user_id' => $apiUserData['user_id'] ?? $currentUser['user_id'] ?? null,
            'username' => $apiUserData['username'] ?? $currentUser['username'] ?? null,
            'email' => $apiUserData['email'] ?? $currentUser['email'] ?? null,
            'phone' => $apiUserData['phone'] ?? $currentUser['phone'] ?? null,
            'is_email_verified' => $apiUserData['is_email_verified'] ?? $currentUser['is_email_verified'] ?? false,
            'is_phone_verified' => $apiUserData['is_phone_verified'] ?? $currentUser['is_phone_verified'] ?? false,
            'user_type' => $apiUserData['user_type'] ?? $currentUser['user_type'] ?? 'regular',
            'balance' => (float)($apiUserData['balance'] ?? $currentUser['balance'] ?? 0)
        ];
    }
}

/**
 * Validation Exception
 */
class ValidationException extends \Exception
{
    private array $errors;

    public function __construct(array $errors)
    {
        $this->errors = $errors;
        parent::__construct('Validation failed');
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
