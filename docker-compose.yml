version: '3'

services:
  php-nginx:
    build: .
    ports:
      - "8080:80"      # Map port 80 for domain access
    volumes:
      - ./src:/var/www/html
      # Map shared assets to both domains
      - ./src/shared/assets:/var/www/html/pay/public/assets
      - ./src/shared/assets:/var/www/html/id/public/assets
      # Map error logs directory
      - ./error_log:/var/log/php
      - ./error_log:/var/log/nginx